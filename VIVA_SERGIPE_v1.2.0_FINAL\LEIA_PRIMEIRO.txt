🎮 VIVA SERGIPE! v1.2.0 - VERSÃO FINAL
=====================================

🏆 PROJETO FINALIZADO COM SUCESSO TOTAL!

Este é o release final do VIVA SERGIPE!, um jogo interativo que utiliza
detecção corporal para preencher o mapa de Sergipe através de movimentos!

📋 CONTEÚDO DO RELEASE:
- ✅ Jogo completo com 6 modos únicos
- ✅ Sistema de 19 conquistas
- ✅ Otimização adaptativa de performance
- ✅ Interface profissional PyQt5
- ✅ Documentação técnica completa
- ✅ Testes automatizados
- ✅ Instalador automático

🚀 COMO EXECUTAR:

Windows:
1. Execute: EXECUTAR_JOGO.bat

Linux/Mac:
1. Execute: ./executar_jogo.sh

Ou manualmente:
1. Instale dependências: pip install -r requirements.txt
2. Execute: python sergipe_game.py

📚 DOCUMENTAÇÃO:
- docs/README.md - Visão geral completa
- docs/COMO_JOGAR.md - Manual do usuário
- docs/MANUAL_TECNICO.md - Documentação técnica
- docs/PROJETO_FINALIZADO.md - Resumo da conclusão

🧪 TESTES:
- tests/ - Suites de testes automatizados
- Execute: python tests/test_v1.2_features.py

⚙️ INSTALAÇÃO AUTOMÁTICA:
- Execute: python installer.py

🎯 REQUISITOS:
- Python 3.7+
- Webcam funcional
- 4GB RAM (8GB recomendado)
- Processador Intel i5 ou equivalente

🎉 DIVIRTA-SE COM O VIVA SERGIPE!

Desenvolvido com ❤️ para a comunidade sergipana
"Sergipe no coração, tecnologia na alma!"

Versão 1.2.0 Final - Janeiro 2025
