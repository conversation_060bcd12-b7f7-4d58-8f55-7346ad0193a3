# VIVA SERGIPE! - Dependências v1.2
# Arquivo de requisitos para instalação automática

# === DEPENDÊNCIAS PRINCIPAIS ===

# Processamento de imagem e câmera
opencv-python>=4.5.0,<5.0.0

# Detecção corporal e pose estimation
mediapipe>=0.8.0,<1.0.0

# Interface gráfica
PyQt5>=5.15.0,<6.0.0

# Áudio e eventos
pygame>=2.0.0,<3.0.0

# Operações matemáticas e arrays
numpy>=1.20.0,<2.0.0

# Monitoramento do sistema
psutil>=5.8.0,<6.0.0

# === DEPENDÊNCIAS OPCIONAIS ===

# Para empacotamento (opcional)
# pyinstaller>=4.5.0

# Para testes (opcional)
# pytest>=6.0.0
# pytest-cov>=2.10.0

# === NOTAS DE INSTALAÇÃO ===

# Para instalar todas as dependências:
# pip install -r requirements.txt

# Para forçar reinstalação completa:
# pip install --force-reinstall -r requirements.txt